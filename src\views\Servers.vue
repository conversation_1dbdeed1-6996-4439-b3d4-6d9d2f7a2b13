<template>
  <div class="servers-page">
    <!-- 统一容器：地图 + 表格 -->
    <div class="servers-container">
      <!-- 世界地图标题栏 -->
      <WorldMap :servers="servers" @refresh="fetchServers" />
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <n-spin size="large">
          <template #description>正在加载节点列表...</template>
        </n-spin>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <n-result status="error" title="加载失败" :description="error">
          <template #footer>
            <n-button @click="fetchServers" type="primary">重试</n-button>
          </template>
        </n-result>
      </div>

      <!-- 空状态 -->
      <div v-else-if="servers.length === 0" class="empty-container">
        <n-empty description="暂无节点数据">
          <template #extra>
            <n-button @click="fetchServers" size="small">刷新</n-button>
          </template>
        </n-empty>
      </div>

      <!-- 服务器表格 -->
      <div v-else class="servers-table-container">
        <!-- 移动端详细服务器列表 -->
        <div class="mobile-server-list" style="display: block; padding: 8px;">
          <div v-for="(server, index) in servers" :key="`mobile-${server.type}-${server.id}`"
               class="mobile-server-item"
               style="background: white; margin-bottom: 10px; padding: 14px; border-radius: 10px; border: 1px solid #e8e8e8; box-shadow: 0 1px 3px rgba(0,0,0,0.08);">

            <!-- 头部：国旗 + 名称 + 状态 -->
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #f5f5f5;">
              <img :src="`/flags/${getCountryInfo(server.name).code.toLowerCase()}.svg`"
                   :alt="getCountryInfo(server.name).name"
                   style="width: 28px; height: 19px; border-radius: 3px; border: 1px solid rgba(0,0,0,0.15); box-shadow: 0 1px 2px rgba(0,0,0,0.1); user-select: none; pointer-events: none;"
                   @error="(e) => e.target.src = '/flags/un.svg'"
                   @contextmenu.prevent
                   draggable="false">
              <div style="flex: 1;">
                <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 2px;">{{ server.name }}</div>
                <div style="display: flex; align-items: center; gap: 6px;">
                  <span :style="{
                    display: 'inline-block',
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    background: isServerOnline(server) ? '#52c41a' : '#ff4d4f'
                  }"></span>
                  <span :style="{
                    fontSize: '11px',
                    fontWeight: '500',
                    color: isServerOnline(server) ? '#52c41a' : '#ff4d4f'
                  }">{{ isServerOnline(server) ? '在线' : '离线' }}</span>
                </div>
              </div>
            </div>

            <!-- 主要信息网格 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px 12px; margin-bottom: 12px;">
              <!-- 类型 -->
              <div style="display: flex; flex-direction: column; gap: 2px;">
                <span style="font-size: 10px; color: #999; font-weight: 500;">协议类型</span>
                <span :style="{
                  fontSize: '12px',
                  fontWeight: '600',
                  color: getTypeColor(server.type),
                  background: getTypeColor(server.type) + '15',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  display: 'inline-block',
                  width: 'fit-content'
                }">{{ getServerTypeLabel(server.type) }}</span>
              </div>

              <!-- 倍率 -->
              <div style="display: flex; flex-direction: column; gap: 2px;">
                <span style="font-size: 10px; color: #999; font-weight: 500;">流量倍率</span>
                <span :style="{
                  fontSize: '12px',
                  fontWeight: '600',
                  color: (server.rate && server.rate > 1) ? '#ff4d4f' : '#52c41a'
                }">{{ server.rate || 1 }}x</span>
              </div>

              <!-- 地址 -->
              <div v-if="config.FEATURES?.SERVERS_SHOW_ADDRESS_COLUMN !== false" style="display: flex; flex-direction: column; gap: 2px; grid-column: 1 / -1;">
                <span style="font-size: 10px; color: #999; font-weight: 500;">服务器地址</span>
                <code style="font-size: 11px; background: #f8f8f8; padding: 3px 6px; border-radius: 4px; color: #666; font-family: Monaco, Consolas, monospace;">{{ server.host }}:{{ server.port }}</code>
              </div>
            </div>

            <!-- 解锁信息区域 -->
            <div v-if="separateTags(server.tags).unlockTags.length > 0" style="margin-bottom: 12px;">
              <div style="font-size: 10px; color: #999; font-weight: 500; margin-bottom: 6px;">流媒体解锁</div>
              <div style="display: flex; gap: 8px; flex-wrap: wrap; align-items: center;">
                <div v-for="tag in separateTags(server.tags).unlockTags" :key="tag"
                     class="mobile-streaming-icon"
                     style="position: relative; display: flex; justify-content: center; align-items: center;">
                  <img
                    :src="getStreamingIconConfig(parseUnlockTag(tag).service).url"
                    :alt="parseUnlockTag(tag).service"
                    :title="`${parseUnlockTag(tag).service}: ${parseUnlockTag(tag).status}${parseUnlockTag(tag).region ? ` (${parseUnlockTag(tag).region})` : ''}`"
                    :style="{
                      width: getStreamingIconConfig(parseUnlockTag(tag).service).size.mobile.width,
                      height: getStreamingIconConfig(parseUnlockTag(tag).service).size.mobile.height,
                      borderRadius: '8px',
                      objectFit: 'contain',
                      filter: 'none',
                      border: (parseUnlockTag(tag).region === 'CN' || (parseUnlockTag(tag).status.includes('No') && parseUnlockTag(tag).region)) && parseUnlockTag(tag).service !== 'YouTube' ? '2px solid #fa8c16' : 'none',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      userSelect: 'none'
                    }"
                    draggable="false"
                    @error="handleMobileIconError"
                    @mouseenter="handleMobileIconHover"
                    @mouseleave="handleMobileIconLeave"
                    @contextmenu.prevent
                  />
                  <!-- 地区限制状态指示器 -->
                  <div v-if="parseUnlockTag(tag).region === 'CN' || (parseUnlockTag(tag).status.includes('No') && parseUnlockTag(tag).region)"
                       style="position: absolute; top: -3px; right: -3px; width: 18px; height: 18px; background: #fa8c16; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 11px; color: white; font-weight: bold; border: 1px solid white;">
                    ⚠
                  </div>
                </div>
              </div>
            </div>



            <!-- 操作按钮 -->
            <div style="display: flex; gap: 8px; padding-top: 8px; border-top: 1px solid #f5f5f5;">
              <button @click="showQRCode(server)" class="btn btn-block">
                <svg class="btn-icon" viewBox="0 0 1024 1024" fill="currentColor">
                  <path d="M516.9664 513.3312m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="currentColor" opacity="0.2"></path>
                  <path d="M447.8464 232.6528H287.4368a43.6224 43.6224 0 0 0-43.6224 43.6224v159.0272a43.6224 43.6224 0 0 0 43.6224 43.6224h160.4096a43.6224 43.6224 0 0 0 43.6224-43.6224V276.2752c0-24.064-19.5072-43.6224-43.6224-43.6224z" fill="currentColor"></path>
                  <path d="M447.8464 532.6336H287.4368a43.6224 43.6224 0 0 0-43.6224 43.6224v159.0272a43.6224 43.6224 0 0 0 43.6224 43.6224h160.4096a43.6224 43.6224 0 0 0 43.6224-43.6224v-159.0784c0-24.0128-19.5072-43.5712-43.6224-43.5712zM588.6464 478.976h160.3584a43.6224 43.6224 0 0 0 43.6224-43.6224V276.2752a43.6224 43.6224 0 0 0-43.6224-43.6224h-160.3584a43.6224 43.6224 0 0 0-43.6224 43.6224v159.0272c-0.0512 24.1152 19.5072 43.6736 43.6224 43.6736z" fill="currentColor"></path>
                  <path d="M577.024 529.8688c-17.7664 0-32.1536 14.3872-32.1536 32.1536v197.632c0 17.7664 14.3872 32.1536 32.1536 32.1536s32.1536-14.3872 32.1536-32.1536v-197.632c0-17.7664-14.3872-32.1536-32.1536-32.1536zM760.7296 529.8688c-17.7664 0-32.1536 14.3872-32.1536 32.1536v197.632c0 17.7664 14.3872 32.1536 32.1536 32.1536s32.1536-14.3872 32.1536-32.1536v-197.632c0-17.7664-14.3872-32.1536-32.1536-32.1536zM671.5392 592.128c-17.7664 0-32.1536 14.3872-32.1536 32.1536v135.3728c0 17.7664 14.3872 32.1536 32.1536 32.1536s32.1536-14.3872 32.1536-32.1536v-135.3728c0-17.7152-14.3872-32.1536-32.1536-32.1536z" fill="currentColor"></path>
                </svg>
                查看二维码
              </button>
            </div>
          </div>
        </div>

        <!-- 桌面端表格（移动端隐藏） -->
        <n-data-table
          :columns="columns"
          :data="servers"
          :loading="loading"
          :pagination="pagination"
          :row-key="(row: Server) => `${row.type}-${row.id}`"
          :row-class-name="getRowClassName"
          size="medium"
          striped
          :scroll-x="1200"
          class="compact-table desktop-table"
        />
      </div>
    </div>

    <!-- 二维码模态框 -->
    <DesktopModal
      :show="showQRModal"
      @update:show="showQRModal = $event"
      title="节点二维码"
      :width="400"
    >
      <div class="qr-modal-content">
        <div class="qr-code-container">
          <div ref="qrCodeRef" class="qr-code"></div>
        </div>
        <div class="qr-actions">
          <button @click="copySubscriptionLink" class="btn">
            复制链接
          </button>
          <button @click="showQRModal = false" class="btn-base btn-large btn-danger">
            关闭
          </button>
        </div>
      </div>
    </DesktopModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h, nextTick, computed } from 'vue'
import { NButton, NSpin, NResult, NEmpty, NDataTable, NTag, NBadge, NTooltip, useMessage } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import { useAuthStore } from '../stores/auth'
import { apiClient, type Server } from '../api/client'
import QRCode from 'qrcode'
import DesktopModal from '../components/DesktopModal.vue'
import WorldMap from '../components/WorldMap.vue'

// 获取配置
const config = (window as any).V2BOARD_CONFIG || {}

// 响应式数据
const servers = ref<Server[]>([])
const loading = ref(false)
const error = ref('')
const message = useMessage()
const authStore = useAuthStore()

// 二维码相关
const showQRModal = ref(false)
const selectedServer = ref<Server | null>(null)
const qrCodeRef = ref<HTMLElement | null>(null)



// 分页配置
const pagination = {
  pageSize: 100,
  showSizePicker: true,
  pageSizes: [20, 50, 100, 200, 500],
  showQuickJumper: true
}

// 基础表格列定义（不包含地址列）
const baseColumns: DataTableColumns<Server> = [
  {
    title: '状态',
    key: 'status',
    width: 50,
    align: 'center',
    render: (row) => {
      const isOnline = isServerOnline(row)
      return h(NBadge, {
        dot: true,
        type: isOnline ? 'success' : 'error',
        processing: isOnline
      }, {
        default: () => h('span', {
          style: {
            color: isOnline ? '#18a058' : '#d03050',
            fontWeight: '600',
            fontSize: '12px'
          }
        }, isOnline ? '在线' : '离线')
      })
    }
  },
  {
    title: '节点名称',
    key: 'name',
    width: 100,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      const countryInfo = getCountryInfo(row.name)
      return h('div', { style: { display: 'flex', alignItems: 'center', gap: '6px' } }, [
        h('img', {
          src: `/flags/${countryInfo.code.toLowerCase()}.svg`,
          alt: countryInfo.name,
          draggable: false,
          style: {
            width: '32px',
            height: '22px',
            borderRadius: '4px',
            border: '1px solid rgba(0,0,0,0.2)',
            flexShrink: '0',
            objectFit: 'cover',
            boxShadow: '0 2px 6px rgba(0,0,0,0.15)',
            userSelect: 'none',
            pointerEvents: 'none'
          },
          onError: (e: Event) => {
            // 如果国旗文件不存在，使用默认图标
            const target = e.target as HTMLImageElement
            target.src = '/flags/un.svg'
          },
          onContextmenu: (e: Event) => {
            e.preventDefault()
          }
        }),
        h('span', { style: { fontWeight: '600' } }, row.name)
      ])
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 50,
    align: 'center',
    render: (row) => {
      const typeColors: Record<string, string> = {
        'shadowsocks': '#1890ff',
        'vmess': '#52c41a',
        'trojan': '#722ed1',
        'hysteria': '#eb2f96',
        'vless': '#fa8c16'
      }
      return h(NTag, {
        type: 'info',
        size: 'small',
        style: {
          backgroundColor: typeColors[row.type] || '#1890ff',
          color: 'white',
          border: 'none'
        }
      }, { default: () => getServerTypeLabel(row.type) })
    }
  }
]

// 地址列定义
const addressColumn = {
  title: '地址',
  key: 'address',
  width: 90,
  ellipsis: {
    tooltip: true
  },
  render: (row: Server) => h('code', {
    style: {
      fontSize: '12px',
      background: 'rgba(0,0,0,0.05)',
      padding: '2px 6px',
      borderRadius: '4px',
      fontFamily: 'Monaco, Consolas, monospace'
    }
  }, `${row.host}:${row.port}`)
}

// 其他列定义（倍率、解锁、操作）
const otherColumns: DataTableColumns<Server> = [
  {
    title: '倍率',
    key: 'rate',
    width: 80,
    align: 'center',
    render: (row) => h('span', {
      style: {
        fontWeight: '600',
        color: row.rate && row.rate > 1 ? '#d03050' : '#18a058'
      }
    }, `${row.rate || 1}x`)
  },
  {
    title: '流媒体解锁',
    key: 'unlock',
    width: 250,
    align: 'center',
    titleAlign: 'center',
    render: (row) => {
      // 从真实的tags数据中提取解锁标签
      const { unlockTags } = separateTags(row.tags)

      if (unlockTags.length === 0) {
        return h('div', {
          style: {
            color: '#999',
            fontSize: '12px',
            textAlign: 'center',
            width: '100%'
          }
        }, '暂无')
      }

      return h('div', {
        style: {
          display: 'flex',
          gap: '6px',
          flexWrap: 'wrap',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '4px 0'
        }
      },
        unlockTags.map((tag: string) => {
          const { service, status, region } = parseUnlockTag(tag)

          // 地区限制判断
          const isRegionRestricted = (region === 'CN' || (status.includes('No') && region)) && service !== 'YouTube'

          // 获取图标配置
          const iconConfig = getStreamingIconConfig(service)

          return h('div', {
            class: 'streaming-icon-wrapper',
            style: {
              position: 'relative',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              cursor: 'pointer'
            },
            title: `${service}: ${status}${region ? ` (${region})` : ''}`
          }, [
            // 流媒体图标
            h('img', {
              src: iconConfig.url,
              alt: service,
              draggable: false,
              style: {
                width: iconConfig.size.desktop.width,
                height: iconConfig.size.desktop.height,
                borderRadius: '6px',
                objectFit: 'contain',
                filter: 'none',
                border: isRegionRestricted ? '2px solid #fa8c16' : 'none',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                userSelect: 'none'
              },
              onMouseenter: (e: Event) => {
                const target = e.target as HTMLImageElement
                target.style.transform = 'scale(1.1)'
                target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)'
              },
              onMouseleave: (e: Event) => {
                const target = e.target as HTMLImageElement
                target.style.transform = 'scale(1)'
                target.style.boxShadow = 'none'
              },
              onContextmenu: (e: Event) => {
                e.preventDefault()
              },
              onError: (e: Event) => {
                const target = e.target as HTMLImageElement
                const currentSrc = target.src
                const serviceName = service.replace('+', '')

                // 尝试所有可能的文件名格式
                const formats = [
                  `${serviceName.charAt(0).toUpperCase() + serviceName.slice(1).toLowerCase()}.svg`, // Netflix.svg
                  `${serviceName}.svg`, // OpenAI.svg (原始名称)
                  `${serviceName.toLowerCase()}.svg`, // netflix.svg
                  `${serviceName.toUpperCase()}.svg`, // NETFLIX.svg
                  `${serviceName.charAt(0).toUpperCase() + serviceName.slice(1).toLowerCase()}.png`, // Netflix.png
                  `${serviceName}.png`, // OpenAI.png (原始名称)
                  `${serviceName.toLowerCase()}.png`, // netflix.png
                  `${serviceName.toUpperCase()}.png` // NETFLIX.png
                ]

                const currentFormat = currentSrc.split('/').pop()
                const currentIndex = formats.indexOf(currentFormat || '')

                if (currentIndex >= 0 && currentIndex < formats.length - 1) {
                  target.src = `/icons/streaming/${formats[currentIndex + 1]}`
                  return
                }

                // 所有格式都失败，显示首字母
                target.style.display = 'none'
                const parent = target.parentElement
                if (parent) {
                  const textNode = document.createElement('div')
                  textNode.textContent = service.charAt(0)
                  textNode.style.cssText = `width:${iconConfig.size.desktop};height:${iconConfig.size.desktop};background:#666;color:white;display:flex;align-items:center;justify-content:center;border-radius:6px;font-size:16px;font-weight:bold`
                  parent.appendChild(textNode)
                }
              }
            }),
            // 状态指示器（地区限制时显示警告）
            (region === 'CN' || (status.includes('No') && region)) ? h('div', {
              style: {
                position: 'absolute',
                top: '-3px',
                right: '-3px',
                width: '16px',
                height: '16px',
                background: '#fa8c16',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px',
                color: 'white',
                fontWeight: 'bold',
                border: '1px solid white'
              }
            }, '⚠') : null
          ])
        })
      )
    }
  },
  {
    title: '操作',
    key: 'qrcode',
    width: 50,
    align: 'center',
    titleAlign: 'center',
    render: (row) => h('div', {
      style: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%'
      }
    }, [
      h(NButton, {
        size: 'small',
        type: 'primary',
        ghost: true,
        onClick: () => showQRCode(row),
        style: {
          padding: '6px',
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      }, {
      default: () => h('svg', {
        width: '18',
        height: '18',
        viewBox: '0 0 1024 1024',
        fill: 'currentColor'
      }, [
        h('path', { d: 'M259.2 262.4h64v64h-64z' }),
        h('path', { d: 'M451.2 134.4h-320v320h320v-320z m-64 256h-192v-192h192v192zM259.2 704h64v64h-64z' }),
        h('path', { d: 'M451.2 576h-320v320h320V576z m-64 256h-192v-192h192v192zM700.8 262.4h64v64h-64z' }),
        h('path', { d: 'M892.8 134.4h-320v320h320v-320z m-64 256h-192v-192h192v192z' }),
        h('path', { d: 'M444.8 531.2v-32H134.4v32z' }),
        h('path', { d: 'M524.8 140.8h-32v748.8h32z' }),
        h('path', { d: 'M854.4 716.8h32v-214.4h-32z' }),
        h('path', { d: 'M755.2 716.8h32v-214.4h-32z' }),
        h('path', { d: 'M665.6 502.4v387.2h32V502.4h-16z' }),
        h('path', { d: 'M579.2 502.4v387.2h32V502.4h-16zM758.4 761.6h128v128h-128z' })
      ])
      })
    ])
  }
]

// 动态表格列定义 - 根据配置决定是否显示地址列
const columns = computed<DataTableColumns<Server>>(() => {
  const showAddressColumn = config.FEATURES?.SERVERS_SHOW_ADDRESS_COLUMN !== false

  if (showAddressColumn) {
    // 插入地址列到类型列之后
    return [
      ...baseColumns,
      addressColumn,
      ...otherColumns
    ]
  } else {
    // 不显示地址列
    return [
      ...baseColumns,
      ...otherColumns
    ]
  }
})

// 行样式函数
const getRowClassName = (row: Server) => {
  return isServerOnline(row) ? 'server-row-online' : 'server-row-offline'
}

// 获取服务器列表
const fetchServers = async () => {
  loading.value = true
  error.value = ''

  try {
    servers.value = await apiClient.getServers()
  } catch (err: any) {
    error.value = err.response?.data?.message || '获取节点列表失败'
  } finally {
    loading.value = false
  }
}

// 判断服务器是否在线
const isServerOnline = (server: Server): boolean => {
  // 优先使用后端计算的 is_online 字段
  if (server.is_online !== undefined) {
    return server.is_online === 1
  }

  // 备用逻辑：如果没有 is_online 字段，使用 last_check_at 判断
  if (!server.last_check_at) return false
  const lastCheck = new Date(server.last_check_at * 1000) // 后端返回的是时间戳（秒）
  const now = new Date()
  const diffSeconds = (now.getTime() - lastCheck.getTime()) / 1000
  return diffSeconds <= 300 // 5分钟内检查过的认为在线，与后端逻辑一致
}

// 获取国家信息
const getCountryInfo = (serverName: string) => {
  const countryMap: Record<string, { name: string; code: string }> = {
    '香港': { name: '香港', code: 'HK' },
    '新加坡': { name: '新加坡', code: 'SG' },
    '美国': { name: '美国', code: 'US' },
    '日本': { name: '日本', code: 'JP' },
    '韩国': { name: '韩国', code: 'KR' },
    '台湾': { name: '台湾', code: 'TW' },
    '泰国': { name: '泰国', code: 'TH' },
    '德国': { name: '德国', code: 'DE' },
    '英国': { name: '英国', code: 'GB' },
    '法国': { name: '法国', code: 'FR' },
    '加拿大': { name: '加拿大', code: 'CA' },
    '澳大利亚': { name: '澳大利亚', code: 'AU' },
    '土耳其': { name: '土耳其', code: 'TR' },
    '波兰': { name: '波兰', code: 'PL' },
    '荷兰': { name: '荷兰', code: 'NL' },
    '芬兰': { name: '芬兰', code: 'FI' },
    '俄罗斯': { name: '俄罗斯', code: 'RU' },
    '印度': { name: '印度', code: 'IN' },
    '巴西': { name: '巴西', code: 'BR' },
    '阿根廷': { name: '阿根廷', code: 'AR' },
    '墨西哥': { name: '墨西哥', code: 'MX' },
    '南非': { name: '南非', code: 'ZA' },
    '埃及': { name: '埃及', code: 'EG' },
    '以色列': { name: '以色列', code: 'IL' },
    '阿联酋': { name: '阿联酋', code: 'AE' },
    '沙特': { name: '沙特', code: 'SA' },
    '马来西亚': { name: '马来西亚', code: 'MY' },
    '印尼': { name: '印尼', code: 'ID' },
    '菲律宾': { name: '菲律宾', code: 'PH' },
    '越南': { name: '越南', code: 'VN' },
    '意大利': { name: '意大利', code: 'IT' },
    '西班牙': { name: '西班牙', code: 'ES' },
    '瑞士': { name: '瑞士', code: 'CH' },
    '瑞典': { name: '瑞典', code: 'SE' },
    '挪威': { name: '挪威', code: 'NO' },
    '丹麦': { name: '丹麦', code: 'DK' },
    '奥地利': { name: '奥地利', code: 'AT' },
    '比利时': { name: '比利时', code: 'BE' },
    '爱尔兰': { name: '爱尔兰', code: 'IE' },
    '葡萄牙': { name: '葡萄牙', code: 'PT' },
    '希腊': { name: '希腊', code: 'GR' },
    '捷克': { name: '捷克', code: 'CZ' },
    '匈牙利': { name: '匈牙利', code: 'HU' },
    '罗马尼亚': { name: '罗马尼亚', code: 'RO' },
    '保加利亚': { name: '保加利亚', code: 'BG' },
    '克罗地亚': { name: '克罗地亚', code: 'HR' },
    '斯洛文尼亚': { name: '斯洛文尼亚', code: 'SI' },
    '斯洛伐克': { name: '斯洛伐克', code: 'SK' },
    '立陶宛': { name: '立陶宛', code: 'LT' },
    '拉脱维亚': { name: '拉脱维亚', code: 'LV' },
    '爱沙尼亚': { name: '爱沙尼亚', code: 'EE' },
    '乌克兰': { name: '乌克兰', code: 'UA' },
    '白俄罗斯': { name: '白俄罗斯', code: 'BY' },
    '摩尔多瓦': { name: '摩尔多瓦', code: 'MD' },
    '塞尔维亚': { name: '塞尔维亚', code: 'RS' },
    '波黑': { name: '波黑', code: 'BA' },
    '北马其顿': { name: '北马其顿', code: 'MK' },
    '阿尔巴尼亚': { name: '阿尔巴尼亚', code: 'AL' },
    '黑山': { name: '黑山', code: 'ME' },
    '冰岛': { name: '冰岛', code: 'IS' },
    '卢森堡': { name: '卢森堡', code: 'LU' },
    '马耳他': { name: '马耳他', code: 'MT' },
    '塞浦路斯': { name: '塞浦路斯', code: 'CY' },
    '新西兰': { name: '新西兰', code: 'NZ' },
    '智利': { name: '智利', code: 'CL' },
    '秘鲁': { name: '秘鲁', code: 'PE' },
    '哥伦比亚': { name: '哥伦比亚', code: 'CO' },
    '委内瑞拉': { name: '委内瑞拉', code: 'VE' },
    '厄瓜多尔': { name: '厄瓜多尔', code: 'EC' },
    '乌拉圭': { name: '乌拉圭', code: 'UY' },
    '巴拉圭': { name: '巴拉圭', code: 'PY' },
    '玻利维亚': { name: '玻利维亚', code: 'BO' },
    '哥斯达黎加': { name: '哥斯达黎加', code: 'CR' },
    '巴拿马': { name: '巴拿马', code: 'PA' },
    '尼加拉瓜': { name: '尼加拉瓜', code: 'NI' },
    '洪都拉斯': { name: '洪都拉斯', code: 'HN' },
    '萨尔瓦多': { name: '萨尔瓦多', code: 'SV' },
    '危地马拉': { name: '危地马拉', code: 'GT' },
    '伯利兹': { name: '伯利兹', code: 'BZ' },
    '牙买加': { name: '牙买加', code: 'JM' },
    '古巴': { name: '古巴', code: 'CU' },
    '多米尼加': { name: '多米尼加', code: 'DO' },
    '海地': { name: '海地', code: 'HT' },
    '巴哈马': { name: '巴哈马', code: 'BS' },
    '巴巴多斯': { name: '巴巴多斯', code: 'BB' },
    '特立尼达': { name: '特立尼达', code: 'TT' },
    // 新增缺失的国家
    'Uzbekistan': { name: '乌兹别克斯坦', code: 'UZ' },
    '乌兹别克斯坦': { name: '乌兹别克斯坦', code: 'UZ' },
    'Turkmenistan': { name: '土库曼斯坦', code: 'TM' },
    '土库曼斯坦': { name: '土库曼斯坦', code: 'TM' },
    'Tajikistan': { name: '塔吉克斯坦', code: 'TJ' },
    '塔吉克斯坦': { name: '塔吉克斯坦', code: 'TJ' },
    'Bhutan': { name: '不丹', code: 'BT' },
    '不丹': { name: '不丹', code: 'BT' }
  }

  for (const [key, value] of Object.entries(countryMap)) {
    if (serverName.includes(key)) {
      return value
    }
  }

  return { name: '未知', code: 'UN' }
}



// 获取服务器类型标签
const getServerTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    'shadowsocks': 'SS',
    'vmess': 'VMess',
    'trojan': 'Trojan',
    'hysteria': 'Hysteria',
    'vless': 'VLESS',
    'tuic': 'TUIC',
    'anytls': 'AnyTLS'
  }
  return typeMap[type] || type.toUpperCase()
}

// 获取协议类型颜色
const getTypeColor = (type: string): string => {
  const typeColors: Record<string, string> = {
    'shadowsocks': '#1890ff',
    'vmess': '#52c41a',
    'trojan': '#722ed1',
    'hysteria': '#eb2f96',
    'vless': '#fa8c16',
    'tuic': '#13c2c2',
    'anytls': '#f759ab'
  }
  return typeColors[type] || '#1890ff'
}

// 解锁服务名称列表（匹配v2board.sh脚本中的服务）
const unlockServices = [
  'Netflix', 'Disney+', 'YouTube', 'OpenAI', 'Bahamut', 'Discovery+', 'Paramount+'
]

// 分离标签：将tags数组分为普通标签和解锁标签
const separateTags = (tags: string[] | undefined): { normalTags: string[], unlockTags: string[] } => {
  if (!tags || tags.length === 0) {
    return { normalTags: [], unlockTags: [] }
  }

  const normalTags: string[] = []
  const unlockTags: string[] = []

  tags.forEach(tag => {
    // 检查是否是解锁标签格式：服务名:状态（如 Netflix:Yes(HK)）
    const isUnlockTag = unlockServices.some(service =>
      tag.startsWith(`${service}:`)
    )

    if (isUnlockTag) {
      unlockTags.push(tag)
    } else {
      normalTags.push(tag)
    }
  })

  return { normalTags, unlockTags }
}

// 格式化解锁标签显示
const formatUnlockTag = (tag: string): string => {
  // 将 "Netflix:Yes(HK)" 格式转换为 "Netflix(HK)" 显示
  const match = tag.match(/^([^:]+):Yes\(([^)]+)\)$/)
  if (match) {
    return `${match[1]}(${match[2]})`
  }

  // 将 "Netflix:Yes" 格式转换为 "Netflix" 显示
  const simpleMatch = tag.match(/^([^:]+):Yes$/)
  if (simpleMatch) {
    return simpleMatch[1]
  }

  // 如果不匹配预期格式，返回原标签
  return tag
}

// 获取流媒体图标配置
const getStreamingIconConfig = (service: string) => {
  // 每个服务的配置（根据实际文件名）
  const serviceConfigs: Record<string, { url: string; size: { desktop: { width: string; height: string }; mobile: { width: string; height: string } } }> = {
    'Netflix': {
      url: '/icons/streaming/netflix.png', // 实际文件名
      size: {
        desktop: { width: '62px', height: '32px' }, // 宽高比约2:1，更紧凑
        mobile: { width: '36px', height: '20px' }
      }
    },
    'Disney+': {
      url: '/icons/streaming/Disney.svg', // 实际文件名
      size: {
        desktop: { width: '62px', height: '32px' },
        mobile: { width: '46px', height: '30px' }
      }
    },
    'YouTube': {
      url: '/icons/streaming/youtube.png', // 实际文件名
      size: {
        desktop: { width: '45px', height: '32px' }, // 保持YouTube的比例
        mobile: { width: '36px', height: '24px' }
      }
    },
    'OpenAI': {
      url: '/icons/streaming/OpenAI.png', // 实际文件名
      size: {
        desktop: { width: '88px', height: '32px' }, // OpenAI是横向长条，高度很小
        mobile: { width: '52px', height: '32px' }
      }
    },
    'Bahamut': {
      url: '/icons/streaming/Bahamut.png', // 实际文件名
      size: {
        desktop: { width: '78px', height: '32px' }, // 适中的宽高比
        mobile: { width: '44px', height: '32px' }
      }
    },
    'Discovery+': {
      url: '/icons/streaming/Discovery.svg', // 实际文件名
      size: {
        desktop: { width: '100px', height: '32px' },
        mobile: { width: '56px', height: '32px' }
      }
    },
    'Discovery': {
      url: '/icons/streaming/Discovery.svg', // 实际文件名
      size: {
        desktop: { width: '32px', height: '24px' },
        mobile: { width: '36px', height: '20px' }
      }
    },
    'Paramount+': {
      url: '/icons/streaming/Paramount.png', // 实际文件名
      size: {
        desktop: { width: '82px', height: '32px' }, // Paramount是很宽的横向图标
        mobile: { width: '56px', height: '26px' }
      }
    }
  }

  // 如果有配置，直接返回
  if (serviceConfigs[service]) {
    return serviceConfigs[service]
  }

  // 默认配置：自动尝试多种格式
  return {
    url: `/icons/streaming/${service.replace('+', '')}.svg`, // 默认尝试SVG
    size: {
      desktop: { width: '32px', height: '24px' }, // 默认4:3比例
      mobile: { width: '36px', height: '20px' }
    }
  }
}

// 获取流媒体图标URL（保持向后兼容）
const getStreamingIconUrl = (service: string): string => {
  return getStreamingIconConfig(service).url
}

// 解析解锁标签
const parseUnlockTag = (tag: string) => {
  // 解析格式：Netflix:Yes(HK) 或 Netflix:Yes 或 Netflix:No
  const match = tag.match(/^([^:]+):(.+)$/)
  if (!match) {
    return { service: tag, status: 'Unknown', region: null }
  }

  const service = match[1]
  const statusPart = match[2]

  // 提取地区信息
  const regionMatch = statusPart.match(/\(([^)]+)\)$/)
  const region = regionMatch ? regionMatch[1] : null
  const status = regionMatch ? statusPart.replace(/\([^)]+\)$/, '') : statusPart

  return { service, status, region }
}

// 移动端图标加载失败处理
const handleMobileIconError = (event: Event) => {
  const target = event.target as HTMLImageElement
  const service = target.alt
  const currentSrc = target.src
  const serviceName = service.replace('+', '')

  // 尝试所有可能的文件名格式
  const formats = [
    `${serviceName.charAt(0).toUpperCase() + serviceName.slice(1).toLowerCase()}.svg`, // Netflix.svg
    `${serviceName}.svg`, // OpenAI.svg (原始名称)
    `${serviceName.toLowerCase()}.svg`, // netflix.svg
    `${serviceName.toUpperCase()}.svg`, // NETFLIX.svg
    `${serviceName.charAt(0).toUpperCase() + serviceName.slice(1).toLowerCase()}.png`, // Netflix.png
    `${serviceName}.png`, // OpenAI.png (原始名称)
    `${serviceName.toLowerCase()}.png`, // netflix.png
    `${serviceName.toUpperCase()}.png` // NETFLIX.png
  ]

  const currentFormat = currentSrc.split('/').pop()
  const currentIndex = formats.indexOf(currentFormat || '')

  if (currentIndex >= 0 && currentIndex < formats.length - 1) {
    target.src = `/icons/streaming/${formats[currentIndex + 1]}`
    return
  }

  // 所有格式都失败，显示首字母
  target.style.display = 'none'
  const parent = target.parentElement
  if (parent) {
    const iconConfig = getStreamingIconConfig(service)
    const textNode = document.createElement('div')
    textNode.textContent = service.charAt(0)
    textNode.style.cssText = `width:${iconConfig.size.mobile};height:${iconConfig.size.mobile};background:#666;color:white;display:flex;align-items:center;justify-content:center;border-radius:8px;font-size:18px;font-weight:bold`
    parent.appendChild(textNode)
  }
}

// 处理移动端图标悬停效果
const handleMobileIconHover = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.transform = 'scale(1.1)'
  img.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)'
}

const handleMobileIconLeave = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.transform = 'scale(1)'
  img.style.boxShadow = 'none'
}

// 复制服务器信息
const copyServerInfo = (server: Server) => {
  const info = `节点: ${server.name}\n类型: ${getServerTypeLabel(server.type)}\n地址: ${server.host}:${server.port}\n倍率: ${server.rate || 1}x`
  navigator.clipboard.writeText(info).then(() => {
    message.success('节点信息已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败')
  })
}

// 连接到服务器
const connectToServer = (server: Server) => {
  if (!isServerOnline(server)) {
    message.warning('该节点当前离线，无法连接')
    return
  }

  // 这里可以添加实际的连接逻辑
  // 比如生成订阅链接、打开客户端等
  message.info(`正在连接到 ${server.name}...`)

  // 示例：复制连接信息
  const connectionInfo = `${server.type}://${server.host}:${server.port}`
  navigator.clipboard.writeText(connectionInfo).then(() => {
    message.success('连接信息已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败')
  })
}

// 显示二维码
const showQRCode = async (server: Server) => {
  selectedServer.value = server
  showQRModal.value = true

  // 等待模态框渲染完成后生成二维码
  await nextTick()
  generateQRCode(server)
}

// 生成二维码
const generateQRCode = async (server: Server) => {
  if (!qrCodeRef.value) return

  // 清空之前的二维码
  qrCodeRef.value.innerHTML = ''

  try {
    // 生成订阅链接
    const subscriptionLink = generateSubscriptionLink(server)
    console.log('生成的订阅链接:', subscriptionLink)

    // 使用 QRCode 库生成二维码图片
    // 使用 QRCode 库生成二维码图片
    const qrCodeDataUrl = await QRCode.toDataURL(subscriptionLink, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    })

    // 创建图片元素显示二维码
    const img = document.createElement('img')
    img.src = qrCodeDataUrl
    img.alt = '节点二维码'
    img.style.cssText = `
      width: 200px;
      height: 200px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    `
    qrCodeRef.value.appendChild(img)
  } catch (error) {
    // 如果生成失败，显示错误信息
    const errorDiv = document.createElement('div')
    errorDiv.style.cssText = `
      width: 200px;
      height: 200px;
      background: #f5f5f5;
      border: 2px dashed #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #999;
      border-radius: 8px;
    `
    errorDiv.textContent = '二维码生成失败'
    qrCodeRef.value.appendChild(errorDiv)
  }
}

// UTF-8 安全的 Base64 编码函数
const utf8ToBase64 = (str: string): string => {
  try {
    // 使用 TextEncoder 将 UTF-8 字符串转换为字节数组
    const encoder = new TextEncoder()
    const bytes = encoder.encode(str)

    // 将字节数组转换为二进制字符串
    let binary = ''
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i])
    }

    // 使用 btoa 进行 Base64 编码
    return btoa(binary)
  } catch (error) {
    console.error('Base64 编码失败:', error)
    // 降级方案：移除非 ASCII 字符后编码
    const asciiStr = str.replace(/[^\x00-\x7F]/g, '')
    return btoa(asciiStr)
  }
}

// 生成订阅链接
const generateSubscriptionLink = (server: Server): string => {
  // 根据服务器类型生成不同格式的订阅链接
  // 这里是示例格式，实际需要根据后端API和具体协议调整

  const baseInfo = `${server.host}:${server.port}`

  switch (server.type) {
    case 'shadowsocks':
      // shadowsocks://method:password@server:port#name
      return `ss://${baseInfo}#${encodeURIComponent(server.name)}`

    case 'vmess':
      // vmess://base64(json)
      const vmessConfig = {
        v: '2',
        ps: server.name,
        add: server.host,
        port: server.port,
        id: 'uuid-placeholder',
        aid: '0',
        net: 'tcp',
        type: 'none',
        host: '',
        path: '',
        tls: ''
      }
      // 使用 UTF-8 安全的 Base64 编码
      return `vmess://${utf8ToBase64(JSON.stringify(vmessConfig))}`

    case 'trojan':
      // trojan://password@server:port#name
      return `trojan://password-placeholder@${baseInfo}#${encodeURIComponent(server.name)}`

    case 'hysteria':
      // hysteria://server:port?auth=password&upmbps=100&downmbps=100#name
      return `hysteria://${baseInfo}?auth=password-placeholder&upmbps=100&downmbps=100#${encodeURIComponent(server.name)}`

    case 'vless':
      // vless://uuid@server:port?type=tcp&security=none#name
      return `vless://uuid-placeholder@${baseInfo}?type=tcp&security=none#${encodeURIComponent(server.name)}`

    default:
      // 通用格式
      return `${server.type}://${baseInfo}#${encodeURIComponent(server.name)}`
  }
}

// 复制订阅链接
const copySubscriptionLink = () => {
  if (!selectedServer.value) return

  const link = generateSubscriptionLink(selectedServer.value)
  navigator.clipboard.writeText(link).then(() => {
    message.success('订阅链接已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败')
  })
}



// 页面挂载时获取数据
onMounted(() => {
  fetchServers()
})
</script>

<style scoped>
@import '@/styles/components/buttons.scss';
.servers-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 页面头部样式已移除，使用WorldMap组件替代 */

/* 工具栏样式已删除 */

.servers-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.servers-table-container {
  background: white;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-top: 1px solid #e0e0e0;
}

/* 表格行样式 */
:deep(.server-row-online) {
  background-color: rgba(24, 160, 88, 0.02);
}

:deep(.server-row-offline) {
  background-color: rgba(208, 48, 80, 0.02);
}

:deep(.server-row-online:hover) {
  background-color: rgba(24, 160, 88, 0.05) !important;
}

:deep(.server-row-offline:hover) {
  background-color: rgba(208, 48, 80, 0.05) !important;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-data-table-tbody .n-data-table-tr:hover .n-data-table-td) {
  background-color: rgba(24, 144, 255, 0.02);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .servers-page {
    padding: 12px;
    min-height: auto; /* 移动端不强制最小高度 */
  }

  .servers-container {
    gap: 12px; /* 减少地图和表格之间的间距 */
    max-width: none; /* 移动端不限制最大宽度 */
    padding: 0 8px; /* 减少左右内边距 */
  }

  .header-content {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .page-title {
    font-size: 20px;
    justify-content: center;
  }

  .header-actions {
    justify-content: center;
  }

  /* 移动端表格容器优化 */
  .servers-table-container {
    margin: 0 -8px; /* 扩展到页面边缘 */
    border-radius: 0; /* 移动端去除圆角 */
    box-shadow: none; /* 简化阴影 */
    border-top: 1px solid #e0e0e0;
    min-height: 200px; /* 确保容器有最小高度 */
    background: white !important; /* 确保背景可见 */
    display: block !important; /* 强制显示 */
  }

  /* 移动端表格滚动优化 */
  .compact-table {
    font-size: 12px; /* 缩小字体适应移动端 */
    width: 100% !important;
  }

  .compact-table :deep(.n-data-table-wrapper) {
    overflow-x: auto; /* 确保横向滚动 */
    min-height: 100px !important; /* 确保表格有最小高度 */
  }

  .compact-table :deep(.n-data-table) {
    min-width: 100% !important;
    width: auto !important;
  }

  .compact-table :deep(.n-data-table-table) {
    width: 100% !important;
    min-width: 600px; /* 移动端最小宽度，确保内容可见 */
  }

  /* 移动端表格强制显示 */
  .mobile-table {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .mobile-table :deep(.n-data-table-wrapper) {
    display: block !important;
    visibility: visible !important;
    height: auto !important;
    min-height: 200px !important;
  }

  .mobile-table :deep(.n-data-table-base-table) {
    display: table !important;
    width: 100% !important;
  }

  .mobile-table :deep(.n-data-table-tbody) {
    display: table-row-group !important;
  }

  .mobile-table :deep(.n-data-table-tr) {
    display: table-row !important;
  }

  .mobile-table :deep(.n-data-table-td) {
    display: table-cell !important;
    padding: 8px 4px !important;
    font-size: 11px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  /* 移动端表格行高优化 */
  .compact-table :deep(.n-data-table-td) {
    padding: 6px 2px !important; /* 进一步压缩间距 */
    font-size: 11px !important;
  }

  .compact-table :deep(.n-data-table-th) {
    padding: 6px 2px !important;
    font-size: 11px !important;
    font-weight: 600;
  }

  /* 移动端显示控制 */
  .mobile-server-list {
    display: block !important;
  }

  .desktop-table {
    display: none !important;
  }
}

/* 桌面端显示控制 */
@media (min-width: 769px) {
  .mobile-server-list {
    display: none !important;
  }

  .desktop-table {
    display: block !important;
  }
}

/* 紧凑表格样式 - 彻底减少列间距 */
.servers-table-container {
  --n-td-padding: 8px 1px;
  --n-th-padding: 8px 1px;
}

.compact-table :deep(.n-data-table-td) {
  padding: 8px 1px !important;
}

.compact-table :deep(.n-data-table-th) {
  padding: 8px 1px !important;
}

.compact-table :deep(.n-data-table-wrapper .n-data-table-table) {
  border-spacing: 0 !important;
}

.compact-table :deep(.n-data-table-table) {
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

/* 特别针对相邻列的间距 */
.compact-table :deep(.n-data-table-td + .n-data-table-td),
.compact-table :deep(.n-data-table-th + .n-data-table-th) {
  border-left: none !important;
}

/* 二维码模态框样式 */
.qr-modal-content {
  text-align: center;
  padding: 24px;
}

.qr-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.qr-code {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-actions {
  margin-top: 0;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.qr-actions button {
  flex: 1;
  min-width: 120px;
  max-width: 160px;
  white-space: nowrap;
}

/* 按钮尺寸样式 */
.btn-large {
  padding: 16px 24px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  min-height: 48px !important;
  border-radius: 12px !important;
}

/* 危险按钮样式 */
.btn-danger {
  background: linear-gradient(135deg, #ff3b30 0%, #d70015 100%) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(255, 59, 48, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #d70015 0%, #b8000f 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(255, 59, 48, 0.4) !important;
}

.btn-danger:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 10px rgba(255, 59, 48, 0.3) !important;
}
</style>
